//
//  DanmakuTrack.swift
//  DanmuKitMac
//
//  Created by Augment Agent on 2025/8/22.
//  Ported from DanmakuKit iOS
//

import Cocoa

protocol DanmakuTrack {

    var positionY: CGFloat { get set }

    var index: UInt { get set }

    var stopClosure: ((_ cell: DanmakuCell) -> Void)? { get set }

    var danmakuCount: Int { get }

    var isOverlap: Bool { get set }

    var playingSpeed: Float { get set }

    init(view: NSView)

    func shoot(danmaku: DanmakuCell)

    func canShoot(danmaku: DanmakuCellModel) -> Bool

    func play()

    func pause()

    func stop()

    func pause(_ danmaku: DanmakuCellModel) -> Bool

    func play(_ danmaku: DanmakuCellModel) -> Bool

    func sync(_ danmaku: DanmakuCell, at progress: Float)

    func syncAndPlay(_ danmaku: DanmakuCell, at progress: Float)

    func canSync(_ danmaku: DanmakuCellModel, at progress: Float) -> Bool

    func clean()

}

class FloatingDanmakuTrack: DanmakuTrack {

    var positionY: CGFloat = 0

    var index: UInt = 0

    var stopClosure: ((DanmakuCell) -> Void)?

    var danmakuCount: Int {
        return cells.count
    }

    var isOverlap = false

    var playingSpeed: Float = 1.0

    private weak var view: NSView?

    private var cells: [DanmakuCell] = []

    required init(view: NSView) {
        self.view = view
    }

    func shoot(danmaku: DanmakuCell) {
        guard danmaku.model != nil else { return }
        cells.append(danmaku)
        // 在 macOS 的 layer-backed NSView 中，不能仅改 layer.position；需要更新 view 的 frame
        // 初始放在右侧视图外，Y 使用轨道中心 positionY
        if let vw = view {
            let originX = vw.bounds.width
            let originY = positionY - danmaku.bounds.height / 2.0
            danmaku.frame = CGRect(x: originX, y: originY, width: danmaku.bounds.width, height: danmaku.bounds.height)
        }
        danmaku.model?.track = index
        prepare(danmaku: danmaku)
        addAnimation(to: danmaku)
        danmaku.enterTrack()
    }

    func canShoot(danmaku: DanmakuCellModel) -> Bool {
        guard !isOverlap else { return true }
        guard let cell = cells.last else { return true }
        guard let cellModel = cell.model else { return true }

        let viewWidth = view?.bounds.width ?? 0
        let preWidth = viewWidth + cell.frame.width
        let nextWidth = viewWidth + danmaku.size.width
        let preRight = max(cell.realFrame.maxX, 0)
        let preCellTime = min(preRight / preWidth * CGFloat(cellModel.displayTime), CGFloat(cellModel.displayTime))
        let distance = viewWidth - preRight - 10
        guard distance >= 0 else { return false }
        let preV = preWidth / CGFloat(cellModel.displayTime)
        let nextV = nextWidth / CGFloat(danmaku.displayTime)
        if nextV - preV <= 0 { return true }
        let time = (distance / (nextV - preV))
        return !(time < preCellTime)
    }

    private func addAnimation(to danmaku: DanmakuCell) {
        guard let model = danmaku.model else { return }

        danmaku.animationBeginTime = CFAbsoluteTimeGetCurrent()
        let rate = max(danmaku.frame.maxX / ((view?.bounds.width ?? 0) + danmaku.frame.width), 0)

        let animation = CABasicAnimation(keyPath: "position.x")
        animation.beginTime = CACurrentMediaTime()
        animation.duration = (model.displayTime * Double(rate)) / Double(playingSpeed)
        animation.fromValue = NSNumber(value: Float(danmaku.layer?.position.x ?? danmaku.frame.midX))
        animation.toValue = NSNumber(value: Float(-danmaku.bounds.width))
        animation.isRemovedOnCompletion = false
        animation.fillMode = .forwards

        animation.delegate = AnimationDelegate { [weak self, weak danmaku] finished in
            guard let self = self, let danmaku = danmaku else { return }
            danmaku.animationTime += (CFAbsoluteTimeGetCurrent() - danmaku.animationBeginTime) * Double(self.playingSpeed)
            if finished {
                // Clear any animations to avoid carry-over when reusing the cell
                danmaku.layer?.removeAllAnimations()
                danmaku.leaveTrack()
                self.stopClosure?(danmaku)
                if let index = self.cells.firstIndex(of: danmaku) {
                    self.cells.remove(at: index)
                }
            }
        }

        danmaku.layer?.add(animation, forKey: "floating")
    }

    func play() {
        cells.forEach { cell in
            addAnimation(to: cell)
        }
    }

    func pause() {
        cells.forEach {
            $0.frame = CGRect(x: $0.realFrame.midX - $0.bounds.width / 2.0, y: $0.realFrame.midY - $0.bounds.height / 2.0, width: $0.bounds.width, height: $0.bounds.height)
            $0.layer?.removeAllAnimations()
        }
    }

    func stop() {
        cells.forEach {
            $0.removeFromSuperview()
            $0.layer?.removeAllAnimations()
        }
        cells.removeAll()
    }

    func pause(_ danmaku: DanmakuCellModel) -> Bool {
        guard let cell = cells.first(where: { $0.model?.identifier == danmaku.identifier }) else { return false }
        if let layer = cell.layer {
            let pausedTime = layer.convertTime(CACurrentMediaTime(), from: nil)
            layer.speed = 0.0
            layer.timeOffset = pausedTime
        }
        return true
    }

    func play(_ danmaku: DanmakuCellModel) -> Bool {
        guard let cell = cells.first(where: { $0.model?.identifier == danmaku.identifier }) else { return false }
        if let layer = cell.layer {
            layer.speed = 1.0
            layer.timeOffset = 0
        }
        return true
    }

    func sync(_ danmaku: DanmakuCell, at progress: Float) {
        guard let model = danmaku.model else { return }
        let totalWidth = view!.frame.width + danmaku.bounds.width
        let syncFrame = CGRect(x: view!.frame.width - totalWidth * CGFloat(progress), y: positionY - danmaku.bounds.height / 2.0, width: danmaku.bounds.width, height: danmaku.bounds.height)
        cells.append(danmaku)
        danmaku.layer?.opacity = 1
        danmaku.frame = syncFrame
        danmaku.model?.track = index
        danmaku.animationTime = model.displayTime * Double(progress)
    }

    func syncAndPlay(_ danmaku: DanmakuCell, at progress: Float) {
        sync(danmaku, at: progress)
        addAnimation(to: danmaku)
    }

    func canSync(_ danmaku: DanmakuCellModel, at progress: Float) -> Bool {
        return canShoot(danmaku: danmaku)
    }

    func clean() {
        stop()
    }

}


// Unified vertical track (used by Top/Bottom wrappers)
class VerticalDanmakuTrack: DanmakuTrack {

    var positionY: CGFloat = 0 {
        didSet {
            // keep centered when layout changes
            guard let vw = view else { return }
            cells.forEach { cell in
                let originX = (vw.bounds.width - cell.bounds.width) / 2.0
                let originY = positionY - cell.bounds.height / 2.0
                cell.frame.origin = CGPoint(x: originX, y: originY)
            }
        }
    }

    var index: UInt = 0
    var stopClosure: ((DanmakuCell) -> Void)?
    var danmakuCount: Int { cells.count }
    var isOverlap = false
    var playingSpeed: Float = 1.0

    private weak var view: NSView?
    private var cells: [DanmakuCell] = []
    private let animationKey: String

    required init(view: NSView) {
        self.view = view
        self.animationKey = "vertical"
    }

    init(view: NSView, animationKey: String) {
        self.view = view
        self.animationKey = animationKey
    }

    func shoot(danmaku: DanmakuCell) {
        guard danmaku.model != nil else { return }
        cells.append(danmaku)
        danmaku.model?.track = index
        if let vw = view {
            let originX = (vw.bounds.width - danmaku.bounds.width) / 2.0
            let originY = positionY - danmaku.bounds.height / 2.0
            danmaku.frame = CGRect(x: originX, y: originY, width: danmaku.bounds.width, height: danmaku.bounds.height)
        }
        danmaku.layer?.opacity = 1
        prepare(danmaku: danmaku)
        danmaku.enterTrack()
        addAnimation(to: danmaku)
    }

    func canShoot(danmaku: DanmakuCellModel) -> Bool {
        return isOverlap ? true : cells.isEmpty
    }

    private func addAnimation(to danmaku: DanmakuCell) {
        guard let model = danmaku.model else { return }
        if let vw = view {
            let originX = (vw.bounds.width - danmaku.bounds.width) / 2.0
            let originY = positionY - danmaku.bounds.height / 2.0
            danmaku.frame.origin = CGPoint(x: originX, y: originY)
        }
        danmaku.animationBeginTime = CFAbsoluteTimeGetCurrent()
        let rate = model.displayTime == 0 ? 0 : (1 - danmaku.animationTime / model.displayTime)
        let animation = CABasicAnimation(keyPath: "opacity")
        animation.fromValue = 1.0
        animation.toValue = 0.0
        animation.duration = 0
        animation.beginTime = CACurrentMediaTime() + model.displayTime * rate / Double(playingSpeed)
        animation.timingFunction = CAMediaTimingFunction(name: .easeIn)
        animation.isRemovedOnCompletion = false
        animation.fillMode = .forwards
        animation.delegate = AnimationDelegate { [weak self, weak danmaku] finished in
            guard let self = self, let danmaku = danmaku else { return }
            danmaku.animationTime += (CFAbsoluteTimeGetCurrent() - danmaku.animationBeginTime) * Double(self.playingSpeed)
            if finished {
                danmaku.layer?.removeAllAnimations()
                danmaku.leaveTrack()
                self.stopClosure?(danmaku)
                if let idx = self.cells.firstIndex(of: danmaku) {
                    self.cells.remove(at: idx)
                }
            }
        }
        danmaku.layer?.add(animation, forKey: animationKey)
    }

    func play() { cells.forEach { addAnimation(to: $0) } }
    func pause() { cells.forEach { $0.layer?.removeAllAnimations() } }
    func stop() { cells.forEach { $0.removeFromSuperview(); $0.layer?.removeAllAnimations() }; cells.removeAll() }

    func pause(_ danmaku: DanmakuCellModel) -> Bool {
        guard let cell = cells.first(where: { $0.model?.identifier == danmaku.identifier }) else { return false }
        if let layer = cell.layer {
            let t = layer.convertTime(CACurrentMediaTime(), from: nil)
            layer.speed = 0.0
            layer.timeOffset = t
        }
        return true
    }

    func play(_ danmaku: DanmakuCellModel) -> Bool {
        guard let cell = cells.first(where: { $0.model?.identifier == danmaku.identifier }) else { return false }
        if let layer = cell.layer {
            layer.speed = 1.0
            layer.timeOffset = 0
        }
        return true
    }

    func sync(_ danmaku: DanmakuCell, at progress: Float) {
        guard let model = danmaku.model else { return }
        cells.append(danmaku)
        danmaku.animationTime = model.displayTime * Double(progress)
        danmaku.model?.track = index
        if let vw = view {
            let originX = (vw.bounds.width - danmaku.bounds.width) / 2.0
            let originY = positionY - danmaku.bounds.height / 2.0
            danmaku.frame = CGRect(x: originX, y: originY, width: danmaku.bounds.width, height: danmaku.bounds.height)
        }
        danmaku.layer?.opacity = 1
    }

    func syncAndPlay(_ danmaku: DanmakuCell, at progress: Float) { sync(danmaku, at: progress); addAnimation(to: danmaku) }
    func canSync(_ danmaku: DanmakuCellModel, at progress: Float) -> Bool { cells.isEmpty }
    func clean() { stop() }
}

// Thin wrappers to keep public API stable
final class TopDanmakuTrack: VerticalDanmakuTrack {
    required init(view: NSView) { super.init(view: view, animationKey: "top") }
}

final class BottomDanmakuTrack: VerticalDanmakuTrack {
    required init(view: NSView) { super.init(view: view, animationKey: "bottom") }
}



// Match iOS helper
private func prepare(danmaku: DanmakuCell) {
    danmaku.animationTime = 0
    danmaku.animationBeginTime = 0
    danmaku.layer?.opacity = 1
}


// Animation delegate helper
class AnimationDelegate: NSObject, CAAnimationDelegate {
    private let completion: (Bool) -> Void

    init(completion: @escaping (Bool) -> Void) {
        self.completion = completion
        super.init()
    }

    func animationDidStop(_ anim: CAAnimation, finished flag: Bool) {
        completion(flag)
    }
}
